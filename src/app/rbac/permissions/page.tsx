import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { PermissionsManagementClient } from "@/components/rbac/permissions-management-client";

export const metadata: Metadata = {
  title: "Permissions | RBAC",
  description: "View and manage permissions for your organization",
};

export default async function PermissionsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is organization admin
  if (session.user.role !== "ORGANIZATION_ADMIN") {
    redirect("/dashboard");
  }

  return <PermissionsManagementClient />;
}

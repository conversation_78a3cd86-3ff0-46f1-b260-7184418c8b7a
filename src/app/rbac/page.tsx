import { <PERSON>ada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { RBACDashboard } from "@/components/rbac/rbac-dashboard";

export const metadata: Metadata = {
  title: "Role & Access Management",
  description: "Manage users, roles, and permissions for your organization",
};

export default async function RBACPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is organization admin
  if (session.user.role !== "ORGANIZATION_ADMIN") {
    redirect("/dashboard");
  }

  return <RBACDashboard />;
}

import { <PERSON>ada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { ReactNode } from "react";

export const metadata: Metadata = {
  title: "Role & Access Management",
  description: "Manage users, roles, and permissions for your organization",
};

interface RBACLayoutProps {
  children: ReactNode;
}

export default async function RBACLayout({ children }: RBACLayoutProps) {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is organization admin
  if (session.user.role !== "ORGANIZATION_ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Role & Access Management</h1>
          <p className="text-muted-foreground">
            Manage users, roles, and permissions for your organization
          </p>
        </div>
      </div>
      {children}
    </div>
  );
}

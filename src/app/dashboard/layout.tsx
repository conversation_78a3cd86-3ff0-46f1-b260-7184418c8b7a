"use client";

import { ReactNode, useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { cn } from "@/lib/utils";
import { NotificationCenter } from "@/components/notifications/notification-center";
import { Button } from "@/components/ui/button";
import { UserAccountNav } from "@/components/auth/user-account-nav";
import { ClientAuthGuard } from "@/components/auth/client-auth-guard";
import { Navbar } from "@/components/layout/navbar";
import {
  LayoutDashboard,
  ShoppingCart,
  Wallet,
  Settings,
  Key,
  Bell,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Menu,
  Folder,
  CreditCard,
  Building2,
  Users,
  UserCog,
  Activity
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { PageTransition } from "@/components/ui/animated";
import { Toolt<PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname();
  const { data: session } = useSession();
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Persist sidebar state in localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-expanded');
    if (savedState !== null) {
      setSidebarExpanded(JSON.parse(savedState));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('sidebar-expanded', JSON.stringify(sidebarExpanded));
  }, [sidebarExpanded]);

  const navigation = [
    { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
    { name: "Projects", href: "/dashboard/projects", icon: Folder },
    { name: "Carbon Credits", href: "/dashboard/carbon-credits", icon: CreditCard },
    { name: "Marketplace", href: "/dashboard/marketplace", icon: ShoppingCart },
    { name: "SPV Management", href: "/dashboard/spv", icon: Building2 },
    { name: "Wallet", href: "/dashboard/wallet", icon: Wallet },
    { name: "Transaction", href: "/dashboard/transactions", icon: ArrowUpDown },
    { name: "Notification", href: "/dashboard/notifications", icon: Bell },
    { name: "Settings", href: "/dashboard/settings", icon: Settings },
  ];

  // Add user management for organization admins (includes both SPV and non-SPV users)
  if (session?.user?.role === "ORGANIZATION_ADMIN") {
    navigation.splice(5, 0, { name: "Users", href: "/dashboard/spv-users", icon: Users });
  }

  // Add RBAC management for organization admins
  if (session?.user?.role === "ORGANIZATION_ADMIN") {
    navigation.push(
      { name: "Role & Access Management", href: "/rbac", icon: UserCog },
    );
  }

  // Debug: Log user role for troubleshooting
  if (typeof window !== 'undefined' && session?.user) {
    console.log('Dashboard Layout - User Role:', session.user.role);
    console.log('Dashboard Layout - Is Org Admin:', session.user.role === "ORGANIZATION_ADMIN");
  }

  // Add admin link if user is an admin
  if (session?.user?.role === "ADMIN") {
    navigation.push({ name: "Super Admin", href: "/admin/dashboard", icon: Key });
  }

  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <ClientAuthGuard>
      <TooltipProvider>
        <div className="flex h-screen bg-green-50">
          {/* Fixed Sidebar */}
          <motion.div
            className="fixed left-0 top-0 z-40 h-full bg-white border-r border-gray-200 shadow-sm hidden md:block"
            animate={{
              width: sidebarExpanded ? '14rem' : '4rem'
            }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            <div className="flex flex-col h-full">
              {/* Header with Logo and Toggle */}
              <div className="flex items-center justify-between h-14 px-4 border-b border-gray-200">
                <motion.div
                  className="flex items-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {!sidebarExpanded ? (
                    <Tooltip delayDuration={300}>
                      <TooltipTrigger asChild>
                        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center cursor-pointer shadow-sm">
                          <span className="text-white font-bold text-sm">C</span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        side="right"
                        className="bg-gray-900 text-white text-sm px-3 py-2 rounded-md shadow-lg border-0"
                        sideOffset={8}
                      >
                        Carbonix
                      </TooltipContent>
                    </Tooltip>
                  ) : (
                    <div className="flex items-center space-x-1.5">
                      <div className="w-5 h-5 bg-gradient-to-br from-green-500 to-blue-600 rounded flex items-center justify-center shadow-sm">
                        <span className="text-white font-bold text-xs">C</span>
                      </div>
                      <motion.h1
                        className="text-sm font-bold text-gray-900"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                      >
                        Carbonix
                      </motion.h1>
                    </div>
                  )}
                </motion.div>

                {/* Toggle Button */}
                <motion.button
                  onClick={toggleSidebar}
                  className="p-0.5 rounded hover:bg-gray-100 transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {sidebarExpanded ? (
                    <ChevronLeft className="h-3 w-3 text-gray-600" />
                  ) : (
                    <ChevronRight className="h-3 w-3 text-gray-600" />
                  )}
                </motion.button>
              </div>
              {/* Navigation Menu */}
              <nav className="flex-1 px-2 py-2 space-y-0.5 overflow-y-auto">
                {navigation.map((item, index) => {
                  // Check if current path matches or starts with the navigation item's path
                  const isActive = pathname === item.href ||
                    (item.href !== '/dashboard' && pathname.startsWith(item.href + '/'));

                  // Create the navigation item
                  const NavItem = (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index, duration: 0.3 }}
                    >
                      <Link
                        href={item.href}
                        className={cn(
                          "group flex items-center px-2 py-1.5 text-xs font-medium rounded transition-all duration-200",
                          isActive
                            ? "bg-green-50 text-green-700 border-l-2 border-green-700"
                            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                          !sidebarExpanded && "justify-center px-1"
                        )}
                      >
                        <motion.div
                          className="flex items-center justify-center"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <item.icon
                            className={cn(
                              "h-3 w-3 transition-colors",
                              isActive ? "text-green-700" : "text-gray-500 group-hover:text-gray-700"
                            )}
                          />
                        </motion.div>

                        <motion.span
                          className="ml-2 truncate text-xs"
                          animate={{
                            opacity: sidebarExpanded ? 1 : 0,
                            width: sidebarExpanded ? 'auto' : 0,
                            marginLeft: sidebarExpanded ? '0.75rem' : 0,
                          }}
                          transition={{ duration: 0.3 }}
                          style={{
                            display: sidebarExpanded ? 'block' : 'none'
                          }}
                        >
                          {item.name}
                        </motion.span>
                      </Link>
                    </motion.div>
                  );

                  // Wrap with tooltip when collapsed (only based on sidebarExpanded state)
                  if (!sidebarExpanded) {
                    return (
                      <Tooltip key={item.name} delayDuration={300}>
                        <TooltipTrigger asChild>
                          {NavItem}
                        </TooltipTrigger>
                        <TooltipContent
                          side="right"
                          className="bg-gray-900 text-white text-sm px-3 py-2 rounded-md shadow-lg border-0"
                          sideOffset={8}
                        >
                          {item.name}
                        </TooltipContent>
                      </Tooltip>
                    );
                  }

                  return NavItem;
                })}
              </nav>


            </div>
          </motion.div>

          {/* Mobile Sidebar Overlay */}
          <AnimatePresence>
            {mobileMenuOpen && (
              <>
                {/* Backdrop */}
                <motion.div
                  className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  onClick={toggleMobileMenu}
                />

                {/* Mobile Sidebar */}
                <motion.div
                  className="fixed left-0 top-0 z-50 h-full w-64 bg-white border-r border-gray-200 shadow-lg md:hidden"
                  initial={{ x: '-100%' }}
                  animate={{ x: 0 }}
                  exit={{ x: '-100%' }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                >
                  <div className="flex flex-col h-full">
                    {/* Mobile Header */}
                    <div className="flex items-center justify-between h-10 px-3 border-b border-gray-200">
                      <h1 className="text-base font-bold text-gray-900">Carbonix</h1>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleMobileMenu}
                      >
                        <ChevronLeft className="h-3 w-3" />
                      </Button>
                    </div>

                    {/* Mobile Navigation */}
                    <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
                      {navigation.map((item) => {
                        // Check if current path matches or starts with the navigation item's path
                        const isActive = pathname === item.href ||
                          (item.href !== '/dashboard' && pathname.startsWith(item.href + '/'));
                        return (
                          <Link
                            key={item.href}
                            href={item.href}
                            onClick={toggleMobileMenu}
                            className={cn(
                              "flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium leading-normal transition-colors",
                              isActive
                                ? "bg-green-50 text-green-700 border-r-2 border-green-700"
                                : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                            )}
                          >
                            <item.icon className={cn("h-5 w-5", isActive ? "text-green-700" : "text-gray-400")} />
                            <span>{item.name}</span>
                          </Link>
                        );
                      })}
                    </nav>


                  </div>
                </motion.div>
              </>
            )}
          </AnimatePresence>

          {/* Main content area */}
          <div
            className="flex flex-col flex-1 transition-all duration-300 ml-0 md:ml-[var(--sidebar-width)]"
            style={{
              '--sidebar-width': sidebarExpanded ? '14rem' : '4rem'
            } as React.CSSProperties}
          >
            {/* Navbar - aligned with sidebar */}
            <div className="relative">
              <Navbar />
            </div>

            {/* Mobile menu button - floating */}
            <Button
              variant="ghost"
              size="icon"
              className="fixed top-4 left-4 z-50 md:hidden bg-white shadow-md hover:shadow-lg"
              onClick={toggleMobileMenu}
            >
              <Menu className="h-5 w-5" />
            </Button>

            {/* Main content - full height with consistent spacing */}
            <main className="flex-1 overflow-y-auto bg-green-50">
              <div className="page-container page-content">
                <PageTransition>
                  {children}
                </PageTransition>
              </div>
            </main>
          </div>
        </div>
      </TooltipProvider>
    </ClientAuthGuard>
  );
}

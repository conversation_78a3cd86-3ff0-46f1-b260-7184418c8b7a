import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';

// Schema for role creation
const createRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(50, 'Role name too long'),
  displayName: z.string().min(1, 'Display name is required').max(100, 'Display name too long'),
  description: z.string().optional(),
  permissions: z.array(z.string()).default([]),
  parentRoleId: z.string().optional(),
});

// Schema for role update
const updateRoleSchema = createRoleSchema.partial();

/**
 * GET /api/rbac/roles
 * Get all custom roles for the organization
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canRead = await hasPermission('read:role', context);
    if (!canRead) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Get roles for the organization
    const roles = await db.customRole.findMany({
      where: { organizationId: user.organizationId },
      include: {
        permissions: {
          include: {
            permission: {
              select: {
                name: true,
                displayName: true,
                description: true,
                category: true,
              },
            },
          },
        },
        userRoles: {
          select: {
            userId: true,
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
        parentRole: {
          select: {
            id: true,
            name: true,
            displayName: true,
          },
        },
        childRoles: {
          select: {
            id: true,
            name: true,
            displayName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Transform the data
    const transformedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description,
      isSystemRole: role.isSystemRole,
      parentRole: role.parentRole,
      childRoles: role.childRoles,
      permissions: role.permissions.map(rp => rp.permission),
      userCount: role.userRoles.length,
      users: role.userRoles.map(ur => ur.user),
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    }));

    return NextResponse.json({
      success: true,
      data: transformedRoles,
    });
  } catch (error) {
    logger.error('Error fetching roles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/rbac/roles
 * Create a new custom role
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canCreate = await hasPermission('create:role', context);
    if (!canCreate) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    const body = await req.json();
    const validatedData = createRoleSchema.parse(body);

    // Check if role name already exists in the organization
    const existingRole = await db.customRole.findFirst({
      where: {
        name: validatedData.name,
        organizationId: user.organizationId,
      },
    });

    if (existingRole) {
      return NextResponse.json(
        { error: 'Role name already exists' },
        { status: 400 }
      );
    }

    // Validate permissions exist
    if (validatedData.permissions.length > 0) {
      const existingPermissions = await db.permission.findMany({
        where: {
          name: { in: validatedData.permissions },
        },
        select: { name: true },
      });

      const existingPermissionNames = existingPermissions.map(p => p.name);
      const invalidPermissions = validatedData.permissions.filter(
        p => !existingPermissionNames.includes(p)
      );

      if (invalidPermissions.length > 0) {
        return NextResponse.json(
          { error: `Invalid permissions: ${invalidPermissions.join(', ')}` },
          { status: 400 }
        );
      }
    }

    // Create role in transaction
    const result = await db.$transaction(async (tx) => {
      // Create the role
      const newRole = await tx.customRole.create({
        data: {
          name: validatedData.name,
          displayName: validatedData.displayName,
          description: validatedData.description,
          organizationId: user.organizationId!,
          parentRoleId: validatedData.parentRoleId,
        },
      });

      // Add permissions if provided
      if (validatedData.permissions.length > 0) {
        const permissions = await tx.permission.findMany({
          where: { name: { in: validatedData.permissions } },
          select: { id: true },
        });

        await tx.rolePermission.createMany({
          data: permissions.map(permission => ({
            roleId: newRole.id,
            permissionId: permission.id,
          })),
        });
      }

      return newRole;
    });

    logger.info('Role created successfully', {
      roleId: result.id,
      roleName: result.name,
      organizationId: user.organizationId,
      createdBy: session.user.id,
    });

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('Error creating role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

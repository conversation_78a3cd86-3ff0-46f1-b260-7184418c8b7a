import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';

/**
 * GET /api/rbac/audit
 * Get RBAC audit logs for the organization
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canView = await hasPermission('view:rbac:audit', context);
    if (!canView) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const userId = searchParams.get('userId') || '';
    const action = searchParams.get('action') || '';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const skip = (page - 1) * limit;

    // Build where clause for permission usage logs
    const whereClause: any = {};

    if (userId) {
      whereClause.userId = userId;
    }

    if (action) {
      whereClause.action = { contains: action, mode: 'insensitive' };
    }

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        whereClause.createdAt.lte = new Date(endDate);
      }
    }

    // Get permission usage logs (this serves as our audit trail)
    const [logs, totalCount] = await Promise.all([
      db.permissionUsageLog.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          permission: {
            select: {
              name: true,
              displayName: true,
              category: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      db.permissionUsageLog.count({ where: whereClause }),
    ]);

    // Get role assignment changes (from UserCustomRole table)
    const roleChanges = await db.userCustomRole.findMany({
      where: {
        user: {
          organizationId: currentUser.organizationId,
        },
        ...(userId && { userId }),
        ...(startDate || endDate ? {
          createdAt: {
            ...(startDate && { gte: new Date(startDate) }),
            ...(endDate && { lte: new Date(endDate) }),
          },
        } : {}),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
            displayName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });

    // Transform permission usage logs
    const transformedLogs = logs.map(log => ({
      id: log.id,
      type: 'permission_usage',
      action: log.action || 'Permission Check',
      user: log.user,
      permission: log.permission,
      resourceType: log.resourceType,
      resourceId: log.resourceId,
      granted: log.granted,
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      metadata: log.metadata,
      timestamp: log.createdAt,
    }));

    // Transform role changes
    const transformedRoleChanges = roleChanges.map(change => ({
      id: change.id,
      type: 'role_assignment',
      action: 'Role Assigned',
      user: change.user,
      role: change.role,
      expiresAt: change.expiresAt,
      timestamp: change.createdAt,
    }));

    // Combine and sort all audit entries
    const allAuditEntries = [...transformedLogs, ...transformedRoleChanges]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);

    // Get summary statistics
    const stats = {
      totalPermissionChecks: await db.permissionUsageLog.count({
        where: {
          user: {
            organizationId: currentUser.organizationId,
          },
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
      totalRoleAssignments: await db.userCustomRole.count({
        where: {
          user: {
            organizationId: currentUser.organizationId,
          },
        },
      }),
      activeUsers: await db.user.count({
        where: {
          organizationId: currentUser.organizationId,
          lastLoginAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    };

    return NextResponse.json({
      success: true,
      data: {
        auditEntries: allAuditEntries,
        stats,
        pagination: {
          page,
          limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error) {
    logger.error('Error fetching audit logs:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { runRbacTests, cleanupTestData } from '@/lib/rbac/test-rbac';

/**
 * POST /api/rbac/test
 * Run RBAC system tests (Admin only)
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin (only admins can run tests)
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await req.json();
    const { cleanup = false } = body;

    // Run the tests
    const testResults = await runRbacTests();

    // Clean up test data if requested
    if (cleanup) {
      await cleanupTestData();
    }

    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    logger.info('RBAC tests completed', {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      runBy: session.user.id,
    });

    return NextResponse.json({
      success: true,
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        successRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
      },
      results: testResults,
      cleanupPerformed: cleanup,
    });
  } catch (error) {
    logger.error('Error running RBAC tests:', error);
    return NextResponse.json(
      { error: 'Failed to run RBAC tests' },
      { status: 500 }
    );
  }
}
